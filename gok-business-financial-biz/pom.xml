<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-financial</artifactId>
        <version>1.0.178</version>
    </parent>

    <artifactId>gok-business-financial-biz</artifactId>
    <packaging>jar</packaging>

    <description>数字财务服务</description>

    <properties>
        <screw.version>0.0.1</screw.version>
        <db2.version>********</db2.version>
        <configuration.version>1.10</configuration.version>
        <maven.deploy.skip>true</maven.deploy.skip>
        <velocity.version>2.3</velocity.version>
        <velocity.tool.version>3.1</velocity.tool.version>
    </properties>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <!--数据操作-->
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-data</artifactId>
            <version>1.0.37</version>
            <exclusions>
                <exclusion>
                    <groupId>com.gok.bcp</groupId>
                    <artifactId>gok-bcp-upms-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
            <version>1.0.37</version>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-security</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.gok.components</groupId>
                    <artifactId>gok-components-excel</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
            <version>3.1.5</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-file-biz</artifactId>
            <version>1.0.104</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-iam</artifactId>
            <version>3.0.62</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud.sdk</groupId>
            <artifactId>huaweicloud-sdk-vod</artifactId>
            <version>3.0.56</version>
        </dependency>
        <!-- Forest -->
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot-starter</artifactId>
            <version>1.7.1</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>com.gok.ehr</groupId>
            <artifactId>ehr-common-core</artifactId>
            <version>0.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-upms-api</artifactId>
            <version>1.1.334</version>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-message-api</artifactId>
            <version>1.1.299</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.9.1</version>
        </dependency>

        <dependency>
            <groupId>com.gok</groupId>
            <artifactId>chanjet.sign.SignatureManage</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.7.0</version>
        </dependency>


        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-file-api</artifactId>
            <version>1.0.131</version>
        </dependency>

        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-log-api</artifactId>
            <version>1.1.262</version>
        </dependency>
        <!-- 数字财务Feign -->
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-financial-api</artifactId>
            <version>1.0.178</version>
        </dependency>
        <!-- 业务一体化Feign -->
        <dependency>
            <groupId>com.gok.ehr</groupId>
            <artifactId>ehr-facade</artifactId>
            <version>0.1.226</version>
            <exclusions>
                <exclusion>
                    <groupId>com.gok.ehr</groupId>
                    <artifactId>ehr-common-excel</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-excel-api</artifactId>
            <version>1.0.178</version>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-task-api</artifactId>
            <version>1.1.333</version>
        </dependency>
        <dependency>
            <groupId>com.gok.ehr</groupId>
            <artifactId>ehr-common-secret</artifactId>
            <version>0.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-admin-api</artifactId>
            <version>1.1.313</version>
        </dependency>

        <!-- HTTP客户端 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.5</version>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>com.gok.business.cloud</groupId>
            <artifactId>gok-business-ehr-api</artifactId>
            <version>1.0.173</version>
        </dependency>


    </dependencies>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <gok-env>dev</gok-env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <gok-env>test</gok-env>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <gok-env>pre</gok-env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <gok-env>prod</gok-env>
            </properties>
        </profile>
    </profiles>
    <build>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>bootstrap.yml</include>
                    <include>pemFile/cjet_pri.pkcs8</include>
                    <include>META-INF/spring.factories</include>
                    <include>logback-spring.xml</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.gok.pboot.financial.FinancialApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
