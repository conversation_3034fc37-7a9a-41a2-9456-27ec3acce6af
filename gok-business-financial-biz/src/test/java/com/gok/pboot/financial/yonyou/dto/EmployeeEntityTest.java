/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工实体类测试
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 员工实体类测试
 * 验证实体类与JSON结构的匹配性
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public class EmployeeEntityTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testEmployeeSaveRequestSerialization() throws Exception {
        // 创建主职信息
        EmployeeSaveRequest.MainJob mainJob = EmployeeSaveRequest.MainJob.builder()
                .director("")
                .dept_id("")
                .job_id("")
                .responsibilities("")
                .begindate("2025-09-26")
                .enddate("2025-09-26")
                .org_id("")
                .psncl_id("")
                .post_id("")
                .jobgrade_id("")
                .objid("")
                .id("")
                .enable(0)
                .creator("")
                .creationtime("2025-09-26 16:27:42")
                .modifier("")
                .modifiedtime("2025-09-26 16:27:42")
                .staff_id("")
                .memo("")
                .showorder(0)
                .businessid("")
                .synchts("2025-09-26 16:27:42")
                .pubts("2025-09-26 16:27:42")
                .lastestjob(0)
                .account_org_id("")
                .new_post_id("")
                .jobrank_id("")
                .sysid("")
                .jsonextattr("")
                .tenantid("")
                .dr(0)
                .trnsevent("")
                .build();

        // 创建兼职信息
        EmployeeSaveRequest.PartTimeJob partTimeJob = EmployeeSaveRequest.PartTimeJob.builder()
                .dept_id("")
                .director("")
                .begindate("2025-09-26")
                .enddate("2025-09-26")
                .responsibilities("")
                .org_id("")
                .job_id("")
                .post_id("")
                .jobgrade_id("")
                .objid("")
                .id("")
                .enable(0)
                .creator("")
                .creationtime("2025-09-26 16:27:42")
                .modifier("")
                .modifiedtime("2025-09-26 16:27:42")
                .staff_id("")
                .memo("")
                .showorder(0)
                .psncl_id("")
                .businessid("")
                .synchts("2025-09-26 16:27:42")
                .account_org_id("")
                .new_post_id("")
                .jobrank_id("")
                .pubts("2025-09-26 16:27:42")
                .sysid("")
                .jsonextattr("")
                .tenantid("")
                .dr(0)
                .trnsevent("")
                .build();

        // 创建银行账户信息
        EmployeeSaveRequest.BankAccount bankAccount = EmployeeSaveRequest.BankAccount.builder()
                .currency("")
                .bank("")
                .isdefault(0)
                .account("")
                .accountname("")
                .memo("")
                .accttype("")
                .bankname("")
                .objid("")
                .id("")
                .enable(0)
                .creator("")
                .creationtime("2025-09-26 16:27:42")
                .modifier("")
                .modifiedtime("2025-09-26 16:27:42")
                .showorder(0)
                .businessid("")
                .synchts("2025-09-26 16:27:42")
                .bu_id("")
                .recordnum(0)
                .staff_id("")
                .pubts("2025-09-26 16:27:42")
                .sysid("")
                .jsonextattr("")
                .tenantid("")
                .dr(0)
                .build();

        // 创建员工数据
        EmployeeSaveRequest.EmployeeData employeeData = EmployeeSaveRequest.EmployeeData.builder()
                .id("")
                .changeUserStatus("")
                .code("")
                .name("")
                .name2("")
                .name3("")
                .name4("")
                .name5("")
                .name6("")
                .creator("")
                .creationtime("2025-09-26 16:27:42")
                .modifier("")
                .modifiedtime("2025-09-26 16:27:42")
                .marital("")
                .education("")
                .email("")
                .birthdate("2025-09-26 16:27:42")
                .ordernumber(0)
                .photo("")
                .mobile("")
                .objid("")
                .cert_type("")
                .cert_no("")
                .officetel("")
                .biz_man_tag(0)
                .shop_assis_tag(0)
                .sex(0)
                .remark("")
                .enable(0)
                .joinworkdate("2025-09-26 16:27:42")
                .user_id("")
                .bu_id("")
                .org_id("")
                .shortname("")
                .linkaddr("")
                .selfemail("")
                .degree("")
                .origin("")
                .country("")
                .political("")
                .nationality("")
                .qq("")
                .weixin("")
                .linkedin("")
                .joinpolitydate("2025-09-26 16:27:42")
                .characterrpr("")
                .permanreside("")
                .bloodtype("")
                .psncl_id("")
                .dept_id("")
                .unit_id("")
                .jsonextattr("")
                .log("")
                .businessid("")
                .synchts("2025-09-26 16:27:42")
                .userrlatid("")
                .majorcorpid("")
                .jobgrade_id("")
                .addr_id("")
                .post_id("")
                .jobrank_id("")
                .tenantid("")
                .sysid("")
                .pubts("2025-09-26 16:27:42")
                .wagegroup("")
                .dr(0)
                ._status("Insert")
                .mainJobList(List.of(mainJob))
                .ptJobList(List.of(partTimeJob))
                .bankAcctList(List.of(bankAccount))
                .build();

        // 创建请求对象
        EmployeeSaveRequest request = EmployeeSaveRequest.builder()
                .data(employeeData)
                .build();

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(request);
        System.out.println("EmployeeSaveRequest JSON:");
        System.out.println(json);

        // 反序列化验证
        EmployeeSaveRequest deserializedRequest = objectMapper.readValue(json, EmployeeSaveRequest.class);
        assert deserializedRequest != null;
        assert deserializedRequest.getData() != null;
        assert deserializedRequest.getData().getMainJobList() != null;
        assert deserializedRequest.getData().getPtJobList() != null;
        assert deserializedRequest.getData().getBankAcctList() != null;
    }

    @Test
    public void testYonyouEmployeeResponseDeserialization() throws Exception {
        // 测试JSON字符串
        String jsonString = """
            {
                "data": [
                    {
                        "id": "",
                        "changeUserStatus": "",
                        "code": "",
                        "name": "",
                        "name2": "",
                        "name3": "",
                        "name4": "",
                        "name5": "",
                        "name6": "",
                        "creator": "",
                        "creationtime": "2025-09-26 16:27:42",
                        "modifier": "",
                        "modifiedtime": "2025-09-26 16:27:42",
                        "marital": "",
                        "education": "",
                        "email": "",
                        "birthdate": "2025-09-26 16:27:42",
                        "ordernumber": 0,
                        "photo": "",
                        "mobile": "",
                        "objid": "",
                        "cert_type": "",
                        "cert_no": "",
                        "officetel": "",
                        "biz_man_tag": 0,
                        "shop_assis_tag": 0,
                        "sex": 0,
                        "remark": "",
                        "enable": 0,
                        "joinworkdate": "2025-09-26 16:27:42",
                        "user_id": "",
                        "bu_id": "",
                        "org_id": "",
                        "shortname": "",
                        "linkaddr": "",
                        "selfemail": "",
                        "degree": "",
                        "origin": "",
                        "country": "",
                        "political": "",
                        "nationality": "",
                        "qq": "",
                        "weixin": "",
                        "linkedin": "",
                        "joinpolitydate": "2025-09-26 16:27:42",
                        "characterrpr": "",
                        "permanreside": "",
                        "bloodtype": "",
                        "psncl_id": "",
                        "dept_id": "",
                        "unit_id": "",
                        "jsonextattr": "",
                        "log": "",
                        "businessid": "",
                        "synchts": "2025-09-26 16:27:42",
                        "userrlatid": "",
                        "majorcorpid": "",
                        "jobgrade_id": "",
                        "addr_id": "",
                        "post_id": "",
                        "jobrank_id": "",
                        "tenantid": "",
                        "sysid": "",
                        "pubts": "2025-09-26 16:27:42",
                        "wagegroup": "",
                        "dr": 0,
                        "mainJobList": [],
                        "ptJobList": [],
                        "bankAcctList": []
                    }
                ]
            }
            """;

        // 反序列化
        YonyouEmployeeResponse response = objectMapper.readValue(jsonString, YonyouEmployeeResponse.class);
        
        // 验证
        assert response != null;
        assert response.getData() != null;
        assert !response.getData().isEmpty();
        
        YonyouEmployeeResponse.EmployeeData employeeData = response.getData().get(0);
        assert employeeData.getMainJobList() != null;
        assert employeeData.getPtJobList() != null;
        assert employeeData.getBankAcctList() != null;
        
        System.out.println("YonyouEmployeeResponse deserialization successful!");
    }
}
