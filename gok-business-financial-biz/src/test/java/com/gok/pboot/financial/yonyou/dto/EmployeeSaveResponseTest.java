package com.gok.pboot.financial.yonyou.dto;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 员工保存响应DTO测试类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class EmployeeSaveResponseTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testEmployeeSaveResponseMapping() {
        // 创建银行账户信息
        EmployeeSaveResponse.BankAccountInfo bankAccount = EmployeeSaveResponse.BankAccountInfo.builder()
                .sourceUnique("")
                .targetUnique("")
                .id("")
                .enable(0)
                .creator("")
                .creationtime("2025-09-26 16:00:57")
                .modifier("")
                .modifiedtime("2025-09-26 16:00:57")
                .memo("")
                .showorder(0)
                .objid("")
                .businessid("")
                .synchts("")
                .accountname("")
                .bank("")
                .account("")
                .bankname("")
                .accttype("")
                .bu_id("")
                .recordnum(0)
                .isdefault(0)
                .currency("")
                .staff_id("")
                .pubts("2025-09-26 16:00:57")
                .sysid("")
                .dr(0)
                .build();

        // 创建主职信息
        EmployeeSaveResponse.MainJobInfo mainJob = EmployeeSaveResponse.MainJobInfo.builder()
                .sourceUnique("")
                .targetUnique("")
                .id("")
                .enable(0)
                .creator("")
                .creationtime("2025-09-26 16:00:57")
                .modifier("")
                .modifiedtime("2025-09-26 16:00:57")
                .staff_id("")
                .org_id("")
                .dept_id("")
                .job_id("")
                .post_id("")
                .jobgrade_id("")
                .begindate("2025-09-26 16:00:57")
                .enddate("2025-09-26 16:00:57")
                .memo("")
                .showorder(0)
                .director("")
                .psncl_id("")
                .objid("")
                .businessid("")
                .synchts("2025-09-26 16:00:57")
                .lastestjob("")
                .account_org_id("")
                .new_post_id("")
                .jobrank_id("")
                .dr(0)
                .build();

        // 创建兼职信息
        EmployeeSaveResponse.PartTimeJobInfo partTimeJob = EmployeeSaveResponse.PartTimeJobInfo.builder()
                .sourceUnique("")
                .targetUnique("")
                .id("")
                .enable(0)
                .creator("")
                .creationtime("2025-09-26 16:00:57")
                .modifier("")
                .modifiedtime("2025-09-26 16:00:57")
                .staff_id("")
                .org_id("")
                .dept_id("")
                .job_id("")
                .post_id("")
                .jobgrade_id("")
                .begindate("2025-09-26 16:00:57")
                .enddate("2025-09-26 16:00:57")
                .memo("")
                .showorder(0)
                .director("")
                .psncl_id("")
                .objid("")
                .synchts("2025-09-26 16:00:57")
                .businessid("")
                .account_org_id("")
                .new_post_id("")
                .jobrank_id("")
                .dr(0)
                .build();

        // 创建员工信息
        EmployeeSaveResponse.EmployeeInfo employeeInfo = EmployeeSaveResponse.EmployeeInfo.builder()
                .data(new Object())
                .sourceUnique("")
                .targetUnique("")
                .code("")
                .name("")
                .mobile("")
                .email("")
                .sex(0)
                .birthdate("2025-09-26 16:00:57")
                .name2("")
                .name3("")
                .name4("")
                .name5("")
                .name6("")
                .creator("")
                .creationtime("2025-09-26 16:00:57")
                .modifier("")
                .modifiedtime("2025-09-26 16:00:57")
                .enable(0)
                .dr(0)
                .photo("")
                .cert_type("")
                .cert_no("")
                .marital("")
                .education("")
                .joinworkdate("2025-09-26 16:00:57")
                .user_id("")
                .bu_id("")
                .org_id("")
                .shortname("")
                .officetel("")
                .linkaddr("")
                .selfemail("")
                .degree("")
                .origin("")
                .country("")
                .political("")
                .nationality("")
                .linkedin("")
                .joinpolitydate("2025-09-26 16:00:57")
                .characterrpr("")
                .permanreside("")
                .bloodtype("")
                .objid("")
                .biz_man_tag(0)
                .ordernumber(0)
                .remark("")
                .shop_assis_tag("")
                .deptId("")
                .psnclId("")
                .unitId("")
                .mainJobList(List.of(mainJob))
                .ptJobList(List.of(partTimeJob))
                .bankAcctList(List.of(bankAccount))
                .build();

        // 创建消息信息
        EmployeeSaveResponse.MessageInfo messageInfo = EmployeeSaveResponse.MessageInfo.builder()
                .sourceUnique("")
                .message("")
                .build();

        // 创建响应数据
        EmployeeSaveResponse.ResponseData responseData = EmployeeSaveResponse.ResponseData.builder()
                .count(0)
                .sucessCount(0)
                .failCount(0)
                .messages(List.of(messageInfo))
                .infos(List.of(employeeInfo))
                .build();

        // 创建响应对象
        EmployeeSaveResponse response = EmployeeSaveResponse.builder()
                .code("")
                .message("")
                .data(responseData)
                .build();

        // 验证对象创建成功
        assertNotNull(response);
        assertNotNull(response.getData());
        assertNotNull(response.getData().getInfos());
        assertFalse(response.getData().getInfos().isEmpty());
        
        EmployeeSaveResponse.EmployeeInfo info = response.getData().getInfos().get(0);
        assertNotNull(info.getMainJobList());
        assertNotNull(info.getPtJobList());
        assertNotNull(info.getBankAcctList());
        
        System.out.println("EmployeeSaveResponse 实体类测试通过！");
    }
}
