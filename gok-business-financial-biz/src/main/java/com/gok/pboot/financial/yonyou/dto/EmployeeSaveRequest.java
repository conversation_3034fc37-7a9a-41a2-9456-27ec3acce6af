/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工保存请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 员工保存请求DTO
 * 用于员工新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeSaveRequest {

    /**
     * 员工数据
     */
    private List<EmployeeData> data;

    /**
     * 员工数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmployeeData {

        /**
         * 员工ID（更新时必填）
         */
        private String id;

        /**
         * 员工编码
         */
        private String code;

        /**
         * 员工姓名
         */
        private String name;

        /**
         * 员工姓名2
         */
        private String name2;

        /**
         * 员工姓名3
         */
        private String name3;

        /**
         * 员工姓名4
         */
        private String name4;

        /**
         * 员工姓名5
         */
        private String name5;

        /**
         * 员工姓名6
         */
        private String name6;

        /**
         * 变更用户状态
         */
        private String changeUserStatus;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 婚姻状况
         */
        private String marital;

        /**
         * 教育程度
         */
        private String education;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 出生日期
         */
        private String birthdate;

        /**
         * 排序号
         */
        private Integer ordernumber;

        /**
         * 照片
         */
        private String photo;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * 证件类型
         */
        private String cert_type;

        /**
         * 证件号码
         */
        private String cert_no;

        /**
         * 办公电话
         */
        private String officetel;

        /**
         * 业务人员标记
         */
        private Integer biz_man_tag;

        /**
         * 商店助理标记
         */
        private Integer shop_assis_tag;

        /**
         * 性别
         */
        private Integer sex;

        /**
         * 备注
         */
        private String remark;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 参加工作日期
         */
        private String joinworkdate;

        /**
         * 用户ID
         */
        private String user_id;

        /**
         * BU ID
         */
        private String bu_id;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 简称
         */
        private String shortname;

        /**
         * 联系地址
         */
        private String linkaddr;

        /**
         * 私人邮箱
         */
        private String selfemail;

        /**
         * 学位
         */
        private String degree;

        /**
         * 籍贯
         */
        private String origin;

        /**
         * 国家
         */
        private String country;

        /**
         * 政治面貌
         */
        private String political;

        /**
         * 国籍
         */
        private String nationality;

        /**
         * QQ
         */
        private String qq;

        /**
         * 微信
         */
        private String weixin;

        /**
         * 领英
         */
        private String linkedin;

        /**
         * 参加政治活动日期
         */
        private String joinpolitydate;

        /**
         * 品德表现
         */
        private String characterrpr;

        /**
         * 户口所在地
         */
        private String permanreside;

        /**
         * 血型
         */
        private String bloodtype;

        /**
         * 人员分类ID
         */
        private String psncl_id;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 单位ID
         */
        private String unit_id;

        /**
         * JSON扩展属性
         */
        private String jsonextattr;

        /**
         * 日志
         */
        private String log;

        /**
         * 业务ID
         */
        private String businessid;

        /**
         * 同步时间戳
         */
        private String synchts;

        /**
         * 用户关系ID
         */
        private String userrlatid;

        /**
         * 主公司ID
         */
        private String majorcorpid;

        /**
         * 职级ID
         */
        private String jobgrade_id;

        /**
         * 地址ID
         */
        private String addr_id;

        /**
         * 岗位ID
         */
        private String post_id;

        /**
         * 职等ID
         */
        private String jobrank_id;

        /**
         * 租户ID
         */
        private String tenantid;

        /**
         * 系统ID
         */
        private String sysid;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 工资组
         */
        private String wagegroup;

        /**
         * 删除标记
         */
        private Integer dr;

        /**
         * 操作状态（Insert:新增，Update:修改）
         */
        private String _status;

        /**
         * 主职列表
         */
        private List<MainJob> mainJobList;

        /**
         * 兼职列表
         */
        private List<PartTimeJob> ptJobList;

        /**
         * 银行账户列表
         */
        private List<BankAccount> bankAcctList;
    }

    /**
     * 主职信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MainJob {

        /**
         * 负责人
         */
        private String director;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 职位ID
         */
        private String job_id;

        /**
         * 职责
         */
        private String responsibilities;

        /**
         * 开始日期
         */
        private String begindate;

        /**
         * 结束日期
         */
        private String enddate;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 人员分类ID
         */
        private String psncl_id;

        /**
         * 岗位ID
         */
        private String post_id;

        /**
         * 职级ID
         */
        private String jobgrade_id;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * ID
         */
        private String id;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 备注
         */
        private String memo;

        /**
         * 显示顺序
         */
        private Integer showorder;

        /**
         * 业务ID
         */
        private String businessid;

        /**
         * 同步时间戳
         */
        private String synchts;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 最新工作
         */
        private Integer lastestjob;

        /**
         * 核算组织ID
         */
        private String account_org_id;

        /**
         * 新岗位ID
         */
        private String new_post_id;

        /**
         * 职等ID
         */
        private String jobrank_id;

        /**
         * 系统ID
         */
        private String sysid;

        /**
         * JSON扩展属性
         */
        private String jsonextattr;

        /**
         * 租户ID
         */
        private String tenantid;

        /**
         * 删除标记
         */
        private Integer dr;

        /**
         * 事务事件
         */
        private String trnsevent;
    }

    /**
     * 兼职信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PartTimeJob {

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 负责人
         */
        private String director;

        /**
         * 开始日期
         */
        private String begindate;

        /**
         * 结束日期
         */
        private String enddate;

        /**
         * 职责
         */
        private String responsibilities;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 职位ID
         */
        private String job_id;

        /**
         * 岗位ID
         */
        private String post_id;

        /**
         * 职级ID
         */
        private String jobgrade_id;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * ID
         */
        private String id;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 备注
         */
        private String memo;

        /**
         * 显示顺序
         */
        private Integer showorder;

        /**
         * 人员分类ID
         */
        private String psncl_id;

        /**
         * 业务ID
         */
        private String businessid;

        /**
         * 同步时间戳
         */
        private String synchts;

        /**
         * 核算组织ID
         */
        private String account_org_id;

        /**
         * 新岗位ID
         */
        private String new_post_id;

        /**
         * 职等ID
         */
        private String jobrank_id;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 系统ID
         */
        private String sysid;

        /**
         * JSON扩展属性
         */
        private String jsonextattr;

        /**
         * 租户ID
         */
        private String tenantid;

        /**
         * 删除标记
         */
        private Integer dr;

        /**
         * 事务事件
         */
        private String trnsevent;
    }

    /**
     * 银行账户信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BankAccount {

        /**
         * 币种
         */
        private String currency;

        /**
         * 银行
         */
        private String bank;

        /**
         * 是否默认
         */
        private Integer isdefault;

        /**
         * 账户
         */
        private String account;

        /**
         * 账户名称
         */
        private String accountname;

        /**
         * 备注
         */
        private String memo;

        /**
         * 账户类型
         */
        private String accttype;

        /**
         * 银行名称
         */
        private String bankname;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * ID
         */
        private String id;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 显示顺序
         */
        private Integer showorder;

        /**
         * 业务ID
         */
        private String businessid;

        /**
         * 同步时间戳
         */
        private String synchts;

        /**
         * BU ID
         */
        private String bu_id;

        /**
         * 记录号
         */
        private Integer recordnum;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 系统ID
         */
        private String sysid;

        /**
         * JSON扩展属性
         */
        private String jsonextattr;

        /**
         * 租户ID
         */
        private String tenantid;

        /**
         * 删除标记
         */
        private Integer dr;
    }
}