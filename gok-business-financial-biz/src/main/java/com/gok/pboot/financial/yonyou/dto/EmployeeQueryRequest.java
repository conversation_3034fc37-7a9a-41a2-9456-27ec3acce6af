/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工查询请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 员工查询请求DTO
 * 用于员工查询操作
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeQueryRequest {

    /**
     * 页码（从1开始）
     */
    private Integer pageIndex;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 员工编码
     */
    private String code;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 部门编码
     */
    private String dept_code;

    /**
     * 部门ID
     */
    private String dept_id;

    /**
     * 启用状态（1:启用，0:停用）
     */
    private Integer enable;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 员工类型
     */
    private String emp_type;

    /**
     * 工号
     */
    private String emp_no;

    /**
     * 创建时间开始
     */
    private String create_time_start;

    /**
     * 创建时间结束
     */
    private String create_time_end;

    /**
     * 修改时间开始
     */
    private String modify_time_start;

    /**
     * 修改时间结束
     */
    private String modify_time_end;
}