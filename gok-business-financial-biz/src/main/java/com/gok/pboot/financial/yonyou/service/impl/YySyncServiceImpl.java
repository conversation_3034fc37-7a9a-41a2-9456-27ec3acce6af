/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * EPM组织服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.pboot.financial.db.entity.YyRelation;
import com.gok.pboot.financial.enums.YyRelateTypeEnum;
import com.gok.pboot.financial.yonyou.client.YonyouClient;
import com.gok.pboot.financial.yonyou.dto.*;
import com.gok.pboot.financial.yonyou.model.YonyouResult;
import com.gok.pboot.financial.yonyou.service.IYyAuthService;
import com.gok.pboot.financial.yonyou.service.IYyRelationService;
import com.gok.pboot.financial.yonyou.service.IYySyncService;
import com.gok.pboot.service.commons.base.ApiResult;
import com.gok.pboot.service.entity.hrm.vo.HrmStaffRosterVo;
import com.gok.pboot.service.feign.RemoteEhrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * EPM组织服务实现类
 * 提供用友开放平台EPM组织相关的业务服务实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class YySyncServiceImpl implements IYySyncService {

    @Resource
    private YonyouClient yonyouClient;

    @Resource
    private IYyAuthService yyAuthService;

    @Resource
    private RemoteOutMultiDeptService remoteOutMultiDeptService;

    @Resource
    private RemoteEhrService remoteEhrService;

    @Resource
    private IYyRelationService yyRelationService;

    /**
     * 查询企业绩效组织体系树
     * 获取用友开放平台企业绩效管理模块的组织体系树结构
     *
     * @return {@link List }<{@link EpmOrgTreeResponse }> 企业绩效组织体系树列表
     */
    public List<EpmOrgTreeResponse> queryEpmOrgTree() {
        // 使用YonyouClient请求
        YonyouResult<List<EpmOrgTreeResponse>> response = yonyouClient.queryEpmOrgTree(yyAuthService.getAccessToken());
        return response.getResult();
    }

    /**
     * 部门保存
     * 用于部门新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
     *
     * @param deptSaveRequest 部门保存请求参数
     * @return {@link DeptSaveResponse} 部门保存响应
     */
    public DeptSaveResponse saveDept(DeptSaveRequest deptSaveRequest) {

        // 获取访问令牌
        String accessToken = yyAuthService.getAccessToken();

        // 调用用友开放平台部门保存接口
        YonyouResult<DeptSaveResponse> response = yonyouClient.saveDept(accessToken, deptSaveRequest);

        return response.getResult();

    }

    /**
     * 企业绩效组织体系成员新增
     * 用于企业绩效组织体系节点新增虚组织
     *
     * @param epmOrgSaveRequest 组织体系成员新增请求参数
     * @return {@link EpmOrgSaveResponse} 组织体系成员新增响应
     */
    public EpmOrgSaveResponse saveEpmOrg(EpmOrgSaveRequest epmOrgSaveRequest) {

        // 获取访问令牌
        String accessToken = yyAuthService.getAccessToken();

        // 调用用友开放平台组织体系成员新增接口
        YonyouResult<EpmOrgSaveResponse> response = yonyouClient.saveEpmOrg(accessToken, epmOrgSaveRequest);

        return response.getResult();

    }

    /**
     * 查询本地部门列表
     * 通过中台服务获取本地部门信息
     *
     * @return {@link List}<{@link Object}> 本地部门列表
     */
    public List<MultiDimensionDeptDto> getLocalDeptList() {

//        // 调用中台服务获取部门列表
//        List<MultiDimensionDeptDto> multiDimensionDeptDtoList = remoteOutMultiDeptService.getDeptList(FinancePropertiesUtils.DEPT_CAT_NAME, null, null)
//                .getData().stream()
//                .filter(dept -> UpmsConstants.STATUS_NORMAL.equals(dept.getStatus()))
//                .collect(Collectors.toList());
//
//        if (CollUtil.isNotEmpty(multiDimensionDeptDtoList)) {
//            return multiDimensionDeptDtoList;
//        } else {
//            return new ArrayList<>();
//        }

        MultiDimensionDeptDto multiDimensionDeptDto = new MultiDimensionDeptDto();
        multiDimensionDeptDto.setDeptId(1L);
        multiDimensionDeptDto.setName("测试部门");
        multiDimensionDeptDto.setCode("7878");
        multiDimensionDeptDto.setParentId(-1L);
        return CollUtil.newArrayList(multiDimensionDeptDto);
    }

    /**
     * 通过中台服务获取合同主体
     *
     * @return {@link List}<{@link MultiDimensionDeptDto}> 本地组织列表
     */
    public List<MultiDimensionDeptDto> getContractSubjectList() {
        // TODO: 实际项目中应该调用中台服务获取组织列表

        // 调用中台服务获取合同主体
//        List<MultiDimensionDeptDto> multiDimensionDeptDtoList = remoteOutMultiDeptService.getDeptList("合同主体", null, null)
//                .getData().stream()
//                .filter(dept -> UpmsConstants.STATUS_NORMAL.equals(dept.getStatus()))
//                .collect(Collectors.toList());
//
//        if (CollUtil.isEmpty(multiDimensionDeptDtoList)) {
//            return new ArrayList<>();
//        }
//
//        MultiDimensionDeptDto topOrg = multiDimensionDeptDtoList.stream()
//                .filter(dept -> Objects.equals(dept.getName(), "福建国科信息科技有限公司"))
//                .findFirst().orElse( null);
//        if (topOrg == null) {
//            return new ArrayList<>();
//        }
//        topOrg.setLevel(1);
//        multiDimensionDeptDtoList.forEach(dept -> {
//            if (!dept.getCode().equals(topOrg.getCode())) {
//                dept.setParentId(topOrg.getDeptId());
//                dept.setLevel(2);
//            }
//        });


        // 这里使用测试数据

        // 顶级组织：福建国科信息科技有限公司
        MultiDimensionDeptDto topOrg = new MultiDimensionDeptDto();
        topOrg.setDeptId(1L);
        topOrg.setName("福建国科信息科技有限公司");
        topOrg.setCode("HTZT000010");
        topOrg.setParentId(-1L);
        topOrg.setLevel(1);

        // 子组织：其他组织作为子组织
        MultiDimensionDeptDto childOrg = new MultiDimensionDeptDto();
        childOrg.setDeptId(2L);
        childOrg.setName("福建国科信息科技有限公司分公司");
        childOrg.setCode("HTZT000022");
        childOrg.setParentId(topOrg.getDeptId());
        childOrg.setLevel(2);

        return CollUtil.newArrayList(topOrg, childOrg)
                .stream()
                .sorted(Comparator.comparing(MultiDimensionDeptDto::getLevel))
                .collect(Collectors.toList());
    }

    /**
     * 将本地部门同步至用友
     * 遍历本地部门列表，将每个部门保存到用友系统中
     *
     */
    @Override
    public void syncDept() {
        log.info("开始同步部门至用友系统");
        try {
            // 获取本地部门列表
            List<MultiDimensionDeptDto> localDeptList = getLocalDeptList();
            Map<Long, MultiDimensionDeptDto> deptDtoMap = CollStreamUtil.toMap(localDeptList, MultiDimensionDeptDto::getDeptId, e -> e);
            // 遍历本地部门列表并同步到用友
            for (MultiDimensionDeptDto dept : localDeptList) {
                try {
                    // 查询是否已存在用友关联关系
                    YyRelation existingRelation = yyRelationService.getByRelateIdAndType(
                            dept.getDeptId(), YyRelateTypeEnum.DEPT.getValue());

                    // 构造部门保存请求
                    DeptSaveRequest deptSaveRequest = buildDeptSaveRequest(dept, existingRelation, deptDtoMap);

                    // 调用部门保存接口
                    DeptSaveResponse response = saveDept(deptSaveRequest);

                    // 保存或更新用友关联关系
                    if (StrUtil.isNotBlank(response.getId())) {
                        yyRelationService.saveOrUpdateRelation(
                                dept.getDeptId(),
                                YyRelateTypeEnum.DEPT.getValue(),
                                response.getId()
                        );
                    }
                } catch (Exception e) {
                    log.error("部门同步失败，部门名称：{}，错误信息：{}", dept.getName(), e.getMessage(), e);
                }
            }

            log.info("本地部门同步至用友系统完成，总共处理：{} 个部门", localDeptList.size());

        } catch (Exception e) {
            log.error("同步本地部门至用友系统异常", e);
            throw new RuntimeException("部门同步失败：" + e.getMessage(), e);
        }
    }

    /**
     * 构造部门保存请求对象
     *
     * @param dept             本地部门信息
     * @param existingRelation 已存在的用友关联关系
     * @return {@link DeptSaveRequest} 部门保存请求对象
     */
    private DeptSaveRequest buildDeptSaveRequest(MultiDimensionDeptDto dept, YyRelation existingRelation, Map<Long, MultiDimensionDeptDto> deptDtoMap) {
        // 构造多语言名称
        DeptSaveRequest.MultiLangName multiLangName = DeptSaveRequest.MultiLangName.builder()
                .zh_CN(dept.getName())
                .build();

        // 判断是新增还是更新操作
        boolean isUpdate = existingRelation != null && StrUtil.isNotBlank(existingRelation.getYyId());
        Long parentId = dept.getParentId();


        // 构造部门数据
        DeptSaveRequest.DeptData deptData = DeptSaveRequest.DeptData.builder()
                .code(dept.getCode())
                .name(multiLangName)
                // 如果是更新操作，需要提供用友ID
                .id(isUpdate ? existingRelation.getYyId() : null)
                // 1:启用
                .enable(1)
                // 根据是否存在关联关系判断操作类型
                ._status(isUpdate ? "Update" : "Insert")
                .build();
        if (parentId < 0) {
//            deptData.setParentorgid_name("福建国科信息科技有限公司");
            deptData.setParentorgCode("00000020");
        } else {
            deptData.setParent_code(deptDtoMap.getOrDefault(parentId, new MultiDimensionDeptDto()).getCode());
        }
        // 构造部门保存请求
        return DeptSaveRequest.builder()
                .data(deptData)
                .build();
    }


    /**
     * 将合同主体同步至用友业务单元
     * 遍历本地合同主体列表，将每个合同主体保存到用友业务单元中
     *
     */
    @Override
    public void syncCompany() {
        log.info("开始同步合同主体至用友业务单元");
        // 获取本地合同主体列表
        List<MultiDimensionDeptDto> contractSubjectList = getContractSubjectList();
        if (CollectionUtils.isEmpty(contractSubjectList)) {
            log.warn("本地合同主体列表为空");
            return;
        }


        int successCount = 0;
        int skipCount = 0;
        int failCount = 0;

        // 创建合同主体映射，用于查找父组织信息
        Map<Long, MultiDimensionDeptDto> contractSubjectMap = CollStreamUtil.toMap(contractSubjectList, MultiDimensionDeptDto::getDeptId, e -> e);

        for (MultiDimensionDeptDto contractSubject : contractSubjectList) {
            try {
                // 查询是否已存在用友关联关系
                YyRelation existingRelation = yyRelationService.getByRelateIdAndType(
                        contractSubject.getDeptId(), YyRelateTypeEnum.COMPANY.getValue());

                // 如果已存在关联关系且业务单元编码已存在，跳过同步
                if (existingRelation != null) {
                    log.info("合同主体[{}]已存在业务单元关联，跳过同步", contractSubject.getCode());
                    skipCount++;
                    continue;
                }

                // 构造业务单元保存请求
                BusinessUnitSaveRequest request = buildBusinessUnitSaveRequest(contractSubject, existingRelation, contractSubjectMap);

                // 调用用友接口保存业务单元
                BusinessUnitSaveResponse response = saveBusinessUnit(request);

                // 保存或更新用友关联关系
                if (StrUtil.isNotBlank(response.getId())) {
                    yyRelationService.saveOrUpdateRelation(
                            contractSubject.getDeptId(),
                            YyRelateTypeEnum.COMPANY.getValue(),
                            response.getId()
                    );
                }

                log.info("合同主体[{}]同步成功", contractSubject.getCode());
                successCount++;


            } catch (Exception e) {
                failCount++;
                log.error("合同主体[{}]同步异常：{}", contractSubject.getCode(), e.getMessage(), e);
            }
        }

        log.info("合同主体同步至业务单元完成：成功{}个，跳过{}个，失败{}个。", successCount, skipCount, failCount);
    }

    /**
     * 业务单元保存
     * 用于业务单元新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
     *
     * @param businessUnitSaveRequest 业务单元保存请求参数
     * @return {@link BusinessUnitSaveResponse} 业务单元保存响应
     */
    public BusinessUnitSaveResponse saveBusinessUnit(BusinessUnitSaveRequest businessUnitSaveRequest) {
        // 获取访问令牌
        String accessToken = yyAuthService.getAccessToken();

        // 调用用友开放平台业务单元保存接口
        YonyouResult<BusinessUnitSaveResponse> response = yonyouClient.saveBusinessUnit(accessToken, businessUnitSaveRequest);

        return response.getResult();
    }


    /**
     * 构造业务单元保存请求对象
     *
     * @param contractSubject    本地合同主体信息
     * @param existingRelation   已存在的用友关联关系
     * @param contractSubjectMap 合同主体映射，用于查找父组织信息
     * @return {@link BusinessUnitSaveRequest} 业务单元保存请求对象
     */
    private BusinessUnitSaveRequest buildBusinessUnitSaveRequest(MultiDimensionDeptDto contractSubject,
                                                                 YyRelation existingRelation,
                                                                 Map<Long, MultiDimensionDeptDto> contractSubjectMap) {
        // 构造多语言名称
        BusinessUnitSaveRequest.MultiLangName multiLangName = BusinessUnitSaveRequest.MultiLangName.builder()
                .zh_CN(contractSubject.getName())
                .build();

        // 判断是新增还是更新操作
        boolean isUpdate = existingRelation != null && StrUtil.isNotBlank(existingRelation.getYyId());
        Long parentId = contractSubject.getParentId();

        // 构造业务单元数据
        BusinessUnitSaveRequest.BusinessUnitData businessUnitData = BusinessUnitSaveRequest.BusinessUnitData.builder()
                .code(contractSubject.getCode())
                .name(multiLangName)
                // 如果是更新操作，需要提供用友ID
                .id(isUpdate ? existingRelation.getYyId() : null)
                // 1:启用
                .enable(1)
                // 根据是否存在关联关系判断操作类型
                ._status(isUpdate ? "Update" : "Insert")
                .build();

        // 设置父业务单元ID
        if (parentId != null && parentId > 0) {
            MultiDimensionDeptDto parentContractSubject = contractSubjectMap.get(parentId);
            if (ObjectUtil.isNotNull(parentContractSubject)) {
                YyRelation relation = yyRelationService.getByRelateIdAndType(parentContractSubject.getDeptId(), YyRelateTypeEnum.COMPANY.getValue());
                if (relation != null) {
                    businessUnitData.setParent(relation.getYyId());
                }
            }
        }

        // 构造业务单元保存请求
        return BusinessUnitSaveRequest.builder()
                .data(businessUnitData)
                .build();
    }


    /**
     * 查询本地员工列表
     *
     * @return {@link List }<{@link HrmStaffRosterVo }>
     */
    public List<HrmStaffRosterVo> getLocalEmployeeList() {

        ApiResult<List<HrmStaffRosterVo>> staffRosterPageVoList = remoteEhrService.getStaffRosterPageVoList(new HashMap<>(0), SecurityConstants.FROM_IN);

        return staffRosterPageVoList.getData();
    }

    /**
     * 同步员工信息到用友系统
     *
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncEmployee() {
        log.info("开始同步员工信息到用友系统");

        try {
            // 获取本地员工数据
            List<HrmStaffRosterVo> localEmployees = getLocalEmployeeList();
            if (CollectionUtils.isEmpty(localEmployees)) {
                log.warn("未找到本地员工数据");
            }

            log.info("获取到本地员工数据 {} 条", localEmployees.size());

            // 获取已存在的用友关联关系
            List<YyRelation> existingRelations = yyRelationService.list(
                    Wrappers.<YyRelation>lambdaQuery()
                            .eq(YyRelation::getRelateType, YyRelateTypeEnum.PERSONNEL.getValue())
            );

            Map<String, YyRelation> relationMap = existingRelations.stream()
                    .collect(Collectors.toMap(relation -> String.valueOf(relation.getRelateId()), Function.identity()));

            int successCount = 0;
            int failCount = 0;

            // 批量处理员工数据
            for (HrmStaffRosterVo employee : localEmployees) {
                try {
                    // 构建员工保存请求
                    EmployeeSaveRequest.EmployeeData employeeData = buildEmployeeSaveRequest(employee, relationMap.get(String.valueOf(employee.getUserId())));


                } catch (Exception e) {
                    failCount++;
                    log.error("员工 {} 同步异常: {}", employee.getWorkCode(), e.getMessage(), e);
                }
            }

            String resultMsg = String.format("员工同步完成，成功: %d 条，失败: %d 条", successCount, failCount);
            log.info(resultMsg);


        } catch (Exception e) {
            log.error("同步员工信息异常", e);
        }
    }


    /**
     * 构建员工保存请求
     * 根据本地员工数据和现有关联关系构建用友员工保存请求
     *
     * @param employee         本地员工数据
     * @param existingRelation 现有关联关系
     * @return {@link EmployeeSaveRequest} 员工保存请求
     */
    private EmployeeSaveRequest.EmployeeData buildEmployeeSaveRequest(HrmStaffRosterVo employee, YyRelation existingRelation) {
        // 构建员工数据
        EmployeeSaveRequest.EmployeeData employeeData = EmployeeSaveRequest.EmployeeData.builder()
                .code(employee.getWorkCode())
                .name(employee.getAliasName())
                .mobile(employee.getMobile())
                .enable(1)
                .sex(employee.getSex() == 1 ? 0 : 1)
                .build();

        // 设置部门信息（如果有部门ID）
        if (employee.getDeptId() != null) {
            // 查找部门关联关系
            YyRelation deptRelation = yyRelationService.getByRelateIdAndType(employee.getDeptId(), YyRelateTypeEnum.DEPT.getValue());
            if (deptRelation != null) {
                employeeData.setDept_id(deptRelation.getYyId());
            }
        }
        EmployeeSaveRequest.MainJob mainJob = EmployeeSaveRequest.MainJob.builder()
                .dept_id(employeeData.getDept_id())
                .begindate(employee.getStartDateStr())
                .enddate(employee.getEndDateStr())
                .build();
        employeeData.setMainJobList(CollUtil.newArrayList(mainJob));
        // 根据是否存在关联关系设置操作状态和ID
        if (existingRelation != null) {
            // 更新操作
            employeeData.setId(existingRelation.getYyId());
            employeeData.set_status("Update");
        } else {
            // 新增操作
            employeeData.set_status("Insert");
        }

        return employeeData;
    }


}