package com.gok.pboot.financial.yonyou.conf;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 用友相关属性
 *
 * <AUTHOR>
 * @date 2025/09/23
 */
@Component
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YonyouProperties {

    @Value("${yonyou.open-api.url}")
    private String openApiUrl;

    @Value("${yonyou.app.key}")
    private String appKey;

    @Value("${yonyou.app.secret}")
    private String appSecret;
    
    @Value("${yonyou.app.tenantId:tenantId}")
    private String tenantId;

    @Value("${yonyou.sync.req-interval:150}")
    private Long reqInterval;

    @Value("${didi.sync.test-filter-enabled:true}")
    private Boolean testFilterEnabled;

    @Value("${didi.sync.test-filter-keyword:滴滴测试}")
    private String testFilterKeyword;
}
