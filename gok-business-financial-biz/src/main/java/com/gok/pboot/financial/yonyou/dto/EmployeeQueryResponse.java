/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工查询响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 员工查询响应DTO
 * 用于接收员工查询操作的响应结果
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeQueryResponse {

    /**
     * 响应状态码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应数据
     */
    private EmployeePageData data;

    /**
     * 员工分页数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmployeePageData {

        /**
         * 总记录数
         */
        private Long total;

        /**
         * 当前页码
         */
        private Integer pageIndex;

        /**
         * 每页大小
         */
        private Integer pageSize;

        /**
         * 总页数
         */
        private Integer totalPages;

        /**
         * 员工列表
         */
        private List<EmployeeInfo> list;
    }

    /**
     * 员工信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmployeeInfo {

        /**
         * 员工ID
         */
        private String id;

        /**
         * 员工编码
         */
        private String code;

        /**
         * 员工姓名
         */
        private String name;

        /**
         * 部门编码
         */
        private String dept_code;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 部门名称
         */
        private String dept_name;

        /**
         * 启用状态（1:启用，0:停用）
         */
        private Integer enable;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 性别（1:男，2:女）
         */
        private Integer gender;

        /**
         * 入职日期
         */
        private String entry_date;

        /**
         * 员工类型
         */
        private String emp_type;

        /**
         * 职位
         */
        private String position;

        /**
         * 工号
         */
        private String emp_no;

        /**
         * 创建时间
         */
        private String create_time;

        /**
         * 修改时间
         */
        private String modify_time;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 修改人
         */
        private String modifier;
    }
}