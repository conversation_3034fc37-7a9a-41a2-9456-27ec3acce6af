/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 员工保存响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 员工保存响应DTO
 * 用于接收员工保存操作的响应结果
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeSaveResponse {


    /**
     * 总数量
     */
    private Integer count;

    /**
     * 成功数量
     */
    private Integer sucessCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 消息列表
     */
    private List<MessageInfo> messages;

    /**
     * 员工信息列表
     */
    private List<EmployeeInfo> infos;


    /**
     * 消息信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MessageInfo {

        /**
         * 源唯一标识
         */
        private String sourceUnique;

        /**
         * 消息内容
         */
        private String message;
    }

    /**
     * 员工信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmployeeInfo {

        /**
         * 数据对象
         */
        private Object data;

        /**
         * 源唯一标识
         */
        private String sourceUnique;

        /**
         * 目标唯一标识
         */
        private String targetUnique;

        /**
         * 员工编码
         */
        private String code;

        /**
         * 员工姓名
         */
        private String name;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 邮箱
         */
        private String email;

        /**
         * 性别（0:女 1:男）
         */
        private Integer sex;

        /**
         * 出生日期
         */
        private String birthdate;

        /**
         * 姓名2
         */
        private String name2;

        /**
         * 姓名3
         */
        private String name3;

        /**
         * 姓名4
         */
        private String name4;

        /**
         * 姓名5
         */
        private String name5;

        /**
         * 姓名6
         */
        private String name6;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 删除标记
         */
        private Integer dr;

        /**
         * 照片
         */
        private String photo;

        /**
         * 证件类型
         */
        private String cert_type;

        /**
         * 证件号码
         */
        private String cert_no;

        /**
         * 婚姻状况
         */
        private String marital;

        /**
         * 学历
         */
        private String education;

        /**
         * 参加工作日期
         */
        private String joinworkdate;

        /**
         * 用户ID
         */
        private String user_id;

        /**
         * 业务单元ID
         */
        private String bu_id;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 简称
         */
        private String shortname;

        /**
         * 办公电话
         */
        private String officetel;

        /**
         * 联系地址
         */
        private String linkaddr;

        /**
         * 个人邮箱
         */
        private String selfemail;

        /**
         * 学位
         */
        private String degree;

        /**
         * 籍贯
         */
        private String origin;

        /**
         * 国家
         */
        private String country;

        /**
         * 政治面貌
         */
        private String political;

        /**
         * 民族
         */
        private String nationality;

        /**
         * LinkedIn
         */
        private String linkedin;

        /**
         * 入党日期
         */
        private String joinpolitydate;

        /**
         * 性格特征
         */
        private String characterrpr;

        /**
         * 常住地址
         */
        private String permanreside;

        /**
         * 血型
         */
        private String bloodtype;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * 业务经理标记
         */
        private Integer biz_man_tag;

        /**
         * 排序号
         */
        private Integer ordernumber;

        /**
         * 备注
         */
        private String remark;

        /**
         * 店铺助理标记
         */
        private String shop_assis_tag;

        /**
         * 部门ID
         */
        private String deptId;

        /**
         * 人员分类ID
         */
        private String psnclId;

        /**
         * 单位ID
         */
        private String unitId;

        /**
         * 主职列表
         */
        private List<MainJobInfo> mainJobList;

        /**
         * 兼职列表
         */
        private List<PartTimeJobInfo> ptJobList;

        /**
         * 银行账户列表
         */
        private List<BankAccountInfo> bankAcctList;
    }

    /**
     * 主职信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MainJobInfo {

        /**
         * 源唯一标识
         */
        private String sourceUnique;

        /**
         * 目标唯一标识
         */
        private String targetUnique;

        /**
         * ID
         */
        private String id;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 职位ID
         */
        private String job_id;

        /**
         * 岗位ID
         */
        private String post_id;

        /**
         * 职级ID
         */
        private String jobgrade_id;

        /**
         * 开始日期
         */
        private String begindate;

        /**
         * 结束日期
         */
        private String enddate;

        /**
         * 备注
         */
        private String memo;

        /**
         * 显示顺序
         */
        private Integer showorder;

        /**
         * 负责人
         */
        private String director;

        /**
         * 人员分类ID
         */
        private String psncl_id;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * 业务ID
         */
        private String businessid;

        /**
         * 同步时间戳
         */
        private String synchts;

        /**
         * 最新职位
         */
        private String lastestjob;

        /**
         * 账套组织ID
         */
        private String account_org_id;

        /**
         * 新岗位ID
         */
        private String new_post_id;

        /**
         * 职等ID
         */
        private String jobrank_id;

        /**
         * 删除标记
         */
        private Integer dr;
    }

    /**
     * 兼职信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PartTimeJobInfo {

        /**
         * 源唯一标识
         */
        private String sourceUnique;

        /**
         * 目标唯一标识
         */
        private String targetUnique;

        /**
         * ID
         */
        private String id;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 组织ID
         */
        private String org_id;

        /**
         * 部门ID
         */
        private String dept_id;

        /**
         * 职位ID
         */
        private String job_id;

        /**
         * 岗位ID
         */
        private String post_id;

        /**
         * 职级ID
         */
        private String jobgrade_id;

        /**
         * 开始日期
         */
        private String begindate;

        /**
         * 结束日期
         */
        private String enddate;

        /**
         * 备注
         */
        private String memo;

        /**
         * 显示顺序
         */
        private Integer showorder;

        /**
         * 负责人
         */
        private String director;

        /**
         * 人员分类ID
         */
        private String psncl_id;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * 同步时间戳
         */
        private String synchts;

        /**
         * 业务ID
         */
        private String businessid;

        /**
         * 账套组织ID
         */
        private String account_org_id;

        /**
         * 新岗位ID
         */
        private String new_post_id;

        /**
         * 职等ID
         */
        private String jobrank_id;

        /**
         * 删除标记
         */
        private Integer dr;
    }

    /**
     * 银行账户信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BankAccountInfo {

        /**
         * 源唯一标识
         */
        private String sourceUnique;

        /**
         * 目标唯一标识
         */
        private String targetUnique;

        /**
         * ID
         */
        private String id;

        /**
         * 启用状态
         */
        private Integer enable;

        /**
         * 创建人
         */
        private String creator;

        /**
         * 创建时间
         */
        private String creationtime;

        /**
         * 修改人
         */
        private String modifier;

        /**
         * 修改时间
         */
        private String modifiedtime;

        /**
         * 备注
         */
        private String memo;

        /**
         * 显示顺序
         */
        private Integer showorder;

        /**
         * 对象ID
         */
        private String objid;

        /**
         * 业务ID
         */
        private String businessid;

        /**
         * 同步时间戳
         */
        private String synchts;

        /**
         * 账户名称
         */
        private String accountname;

        /**
         * 银行
         */
        private String bank;

        /**
         * 账户
         */
        private String account;

        /**
         * 银行名称
         */
        private String bankname;

        /**
         * 账户类型
         */
        private String accttype;

        /**
         * 业务单元ID
         */
        private String bu_id;

        /**
         * 记录号
         */
        private Integer recordnum;

        /**
         * 是否默认
         */
        private Integer isdefault;

        /**
         * 币种
         */
        private String currency;

        /**
         * 员工ID
         */
        private String staff_id;

        /**
         * 发布时间戳
         */
        private String pubts;

        /**
         * 系统ID
         */
        private String sysid;

        /**
         * 删除标记
         */
        private Integer dr;
    }
}