package com.gok.pboot.financial.yonyou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.db.entity.YyRelation;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用友关联表Service接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IYyRelationService extends IService<YyRelation> {

    /**
     * 根据关联ID和关联类型查询用友关联关系
     *
     * @param relateId 关联ID
     * @param relateType 关联类型
     * @return 用友关联关系
     */
    YyRelation getByRelateIdAndType(Long relateId, Integer relateType);

    /**
     * 根据关联ID列表和关联类型查询用友关联关系列表
     *
     * @param relateIds 关联ID列表
     * @param relateType 关联类型
     * @return 用友关联关系列表
     */
    List<YyRelation> listByRelateIdsAndType(List<Long> relateIds, Integer relateType);

    /**
     * 根据用友ID查询关联关系
     *
     * @param yyId 用友ID
     * @return 用友关联关系
     */
    YyRelation getByYyId(String yyId);

    /**
     * 保存或更新用友关联关系
     *
     * @param relateId 关联ID
     * @param relateType 关联类型
     * @param yyId 用友ID
     */
    void saveOrUpdateRelation(Long relateId, Integer relateType, String yyId);

    /**
     * 批量保存或更新用友关联关系
     *
     * @param relations 用友关联关系列表
     * @return 是否成功
     */
    boolean batchSaveOrUpdate(List<YyRelation> relations);

    /**
     * 更新同步时间
     *
     * @param id 关联关系ID
     * @param syncTime 同步时间
     * @return 是否成功
     */
    boolean updateSyncTime(Long id, LocalDateTime syncTime);
}
